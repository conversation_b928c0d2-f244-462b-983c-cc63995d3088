# 画布平移式全屏思维导图系统使用指南

## 功能概述

画布平移式全屏思维导图系统提供了更自然的全屏体验，通过画布平移和缩放来实现全屏效果，配合底部浮窗按钮进行导航：

### 1. 画布模式（Canvas Mode）
- **用途**：整体导航和结构预览
- **特点**：
  - 无限画布，支持自由拖拽和缩放
  - 扇形连接线，清晰的层级关系
  - 水平对齐的节点布局
  - 智能避让，无重叠卡片
  - 改进的层级排序，避免卡片遮盖

### 2. 画布平移式全屏模式（Canvas Pan Full Screen Mode）
- **用途**：专注内容阅读和深度浏览
- **特点**：
  - 画布自动平移和缩放到目标节点
  - 目标节点居中显示，放大2倍
  - 底部弹出浮窗式操作按钮
  - 保持画布的连续性和空间感

## 核心交互流程

### 自动画布平移触发
```dart
// 当用户展开任意节点时，自动进入画布平移式全屏模式
await provider.expandNode(nodeId);
// 系统自动调用：
// 1. provider._enterFullScreenMode(nodeId);
// 2. _panToNodeFullScreen(nodeId, nodes, origin);
```

### 画布平移式全屏导航
```dart
// 点击底部浮窗按钮时
await provider.expandAndShowFullScreen(childNodeId);
// 1. 关闭当前底部面板
// 2. 展开子节点（如果尚未展开）
// 3. 画布平移到该子节点
// 4. 显示新的底部浮窗按钮
```

### 手动模式切换
```dart
// 手动进入画布平移式全屏模式
provider.enterFullScreenMode(nodeId);

// 退出全屏模式，回到正常画布模式
provider.exitFullScreenMode();

// 切换底部按钮显示状态
provider.toggleBottomButtons();
```

## 状态管理

### 画布平移式全屏模式状态
- `isFullScreenMode`: 当前是否处于画布平移式全屏模式
- `currentFullScreenNodeId`: 当前全屏显示的节点ID
- `currentFullScreenNode`: 当前全屏显示的节点对象
- `showBottomButtons`: 是否显示底部浮窗按钮

### 数据同步
- 画布模式和全屏模式共享同一份数据和画布状态
- 全屏模式通过画布变换实现，保持空间连续性
- 支持无缝切换，不丢失用户操作状态
- 底部按钮状态独立管理，可单独控制显示隐藏

## 组件架构

### MindMapProvider 增强
```dart
class MindMapProvider extends ChangeNotifier {
  // 全屏模式状态
  bool _isFullScreenMode = false;
  String? _currentFullScreenNodeId;
  
  // 全屏模式控制方法
  void enterFullScreenMode(String nodeId);
  void exitFullScreenMode();
  Future<void> expandAndShowFullScreen(String nodeId);
  List<KnowledgeNode> getCurrentFullScreenChildren();
}
```

### FullScreenCard 组件
```dart
class FullScreenCard extends StatefulWidget {
  final KnowledgeNode node;
  final VoidCallback? onExit;
  
  // 功能特性：
  // - 全屏内容显示
  // - 动画过渡效果
  // - 子节点按钮布局
  // - 退出全屏功能
}
```

### MindMapCanvas 双模式集成
```dart
Widget build(BuildContext context) {
  return Stack(
    children: [
      // 画布模式（始终存在）
      canvasWidget,
      
      // 全屏模式（条件显示）
      if (provider.isFullScreenMode && provider.currentFullScreenNode != null)
        FullScreenCard(
          node: provider.currentFullScreenNode!,
          onExit: () => provider.exitFullScreenMode(),
        ),
    ],
  );
}
```

## 动画效果

### 全屏模式进入动画
- 缩放动画：从 0.8 到 1.0
- 淡入动画：从 0.0 到 1.0
- 缓动曲线：easeOutBack + easeOut

### 按钮交互动画
- 点击反馈：先缩小再恢复
- 切换动画：淡出 → 切换内容 → 淡入
- 持续时间：300ms

## 使用建议

### 适用场景
1. **内容浏览**：使用全屏模式深度阅读节点内容
2. **结构导航**：使用画布模式了解整体结构
3. **快速探索**：通过全屏模式的按钮快速跳转

### 最佳实践
1. 展开节点时自动进入全屏模式，专注内容
2. 需要查看整体结构时退出全屏模式
3. 利用子节点按钮进行线性浏览
4. 保持两种模式的数据同步

## 技术特性

### 性能优化
- 画布模式作为背景层，避免重复渲染
- 全屏模式使用条件渲染，按需显示
- 动画控制器合理管理，避免内存泄漏

### 响应式设计
- 全屏模式自适应屏幕尺寸
- 按钮布局响应式调整
- 安全区域适配

### 可扩展性
- 支持自定义动画效果
- 可配置的按钮样式
- 灵活的状态管理接口

这个双模式交互系统为用户提供了既能宏观把控又能微观深入的完整思维导图体验。
