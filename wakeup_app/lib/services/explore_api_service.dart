import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../core/services/unified_api_service.dart';
import '../models/explore_model.dart';
import '../providers/user_provider.dart';
import '../core/utils/logger.dart';

/// 探索页面专用API服务类
/// 处理知识获取、AI问答、关联问题生成等后端交互
class ExploreApiService {
  static final ExploreApiService _instance = ExploreApiService._internal();
  factory ExploreApiService() => _instance;
  ExploreApiService._internal();

  final UnifiedApiService _apiService = UnifiedApiService();
  final TaggedLogger _logger = const TaggedLogger('ExploreAPI');

  /// 获取初始知识内容
  /// 用于页面首次加载时展示默认知识卡片
  Future<KnowledgeCard?> getInitialKnowledge(BuildContext? context) async {
    try {
      _logger.info('获取初始知识内容');

      final response = await _apiService.get<Map<String, dynamic>>(
        '/api/explore/initial',
        context: context,
      );

      if (response.isSuccess && response.data != null) {
        return KnowledgeCard.fromJson(response.data!);
      } else {
        _logger.warning('获取初始知识失败: ${response.error?.toString() ?? "未知错误"}');
        // 返回默认知识卡片
        return _getDefaultKnowledge();
      }
    } catch (e) {
      _logger.error('获取初始知识异常', error: e);
      return _getDefaultKnowledge();
    }
  }

  /// 向AI提问并获取回答
  /// 处理用户输入的问题，返回AI生成的知识卡片
  Future<KnowledgeCard?> askAiQuestion({
    required String question,
    String? context,
    BuildContext? buildContext,
  }) async {
    try {
      _logger.info('向AI提问: $question');

      // 获取用户ID
      String? userId;
      if (buildContext != null) {
        try {
          final userProvider = Provider.of<UserProvider>(
            buildContext,
            listen: false,
          );
          userId = userProvider.userId.toString();
        } catch (e) {
          _logger.warning('无法获取用户ID: $e');
        }
      }

      final request = AiQuestionRequest(
        question: question,
        context: context,
        userId: userId,
      );

      final response = await _apiService.post<Map<String, dynamic>>(
        '/api/explore/ai-question',
        body: request.toJson(),
        context: buildContext,
      );

      if (response.isSuccess && response.data != null) {
        final aiResponse = AiQuestionResponse.fromJson(response.data!);

        // 将AI回答转换为知识卡片
        return KnowledgeCard(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          questionTitle: question,
          content: aiResponse.answer,
          createdAt: DateTime.now(),
          sourceType: 'ai_generated',
          metadata: {
            'confidence': aiResponse.confidence,
            'ai_metadata': aiResponse.metadata,
          },
        );
      } else {
        _logger.warning('AI问答失败: ${response.error?.toString() ?? "未知错误"}');
        return null;
      }
    } catch (e) {
      _logger.error('AI问答异常', error: e);
      return null;
    }
  }

  /// 获取关联问题列表
  /// 基于当前知识内容生成相关的学习问题
  Future<List<RelatedQuestion>> getRelatedQuestions({
    String? currentKnowledgeId,
    String? context,
    BuildContext? buildContext,
  }) async {
    try {
      _logger.info('获取关联问题');

      final queryParams = <String, dynamic>{};
      if (currentKnowledgeId != null) {
        queryParams['knowledge_id'] = currentKnowledgeId;
      }
      if (context != null) {
        queryParams['context'] = context;
      }

      final response = await _apiService.get<List<dynamic>>(
        '/api/explore/related-questions',
        queryParams: queryParams,
        context: buildContext,
        parser: (data) => data is List ? data : [data],
      );

      if (response.isSuccess && response.data != null) {
        return response.data!
            .map(
              (item) => RelatedQuestion.fromJson(item as Map<String, dynamic>),
            )
            .toList();
      } else {
        _logger.warning('获取关联问题失败: ${response.error?.toString() ?? "未知错误"}');
        return _getDefaultRelatedQuestions();
      }
    } catch (e) {
      _logger.error('获取关联问题异常', error: e);
      return _getDefaultRelatedQuestions();
    }
  }

  /// 生成AI关联问题
  /// 基于当前知识内容，让AI动态生成新的相关问题
  Future<List<RelatedQuestion>> generateAiRelatedQuestions({
    required String currentContent,
    String? questionTitle,
    BuildContext? buildContext,
  }) async {
    try {
      _logger.info('生成AI关联问题');

      final requestBody = {
        'current_content': currentContent,
        'question_title': questionTitle,
        'generate_count': 3, // 生成3个问题（左、中、右卡片）
      };

      final response = await _apiService.post<List<dynamic>>(
        '/api/explore/generate-related',
        body: requestBody,
        context: buildContext,
        parser: (data) => data is List ? data : [data],
      );

      if (response.isSuccess && response.data != null) {
        return response.data!
            .map(
              (item) => RelatedQuestion.fromJson(item as Map<String, dynamic>),
            )
            .toList();
      } else {
        _logger.warning('生成AI关联问题失败: ${response.error?.toString() ?? "未知错误"}');
        return _getDefaultRelatedQuestions();
      }
    } catch (e) {
      _logger.error('生成AI关联问题异常', error: e);
      return _getDefaultRelatedQuestions();
    }
  }

  /// 获取特定知识详情
  /// 当用户点击关联问题时，获取对应的详细知识内容
  Future<KnowledgeCard?> getKnowledgeDetail({
    required String questionId,
    BuildContext? context,
  }) async {
    try {
      _logger.info('获取知识详情: $questionId');

      final response = await _apiService.get<Map<String, dynamic>>(
        '/api/explore/knowledge/$questionId',
        context: context,
      );

      if (response.isSuccess && response.data != null) {
        return KnowledgeCard.fromJson(response.data!);
      } else {
        _logger.warning('获取知识详情失败: ${response.error?.toString() ?? "未知错误"}');
        return null;
      }
    } catch (e) {
      _logger.error('获取知识详情异常', error: e);
      return null;
    }
  }

  /// 记录用户学习行为
  /// 用于后端分析和个性化推荐
  Future<void> recordLearningBehavior({
    required String action,
    String? knowledgeId,
    String? questionId,
    Map<String, dynamic>? additionalData,
    BuildContext? context,
  }) async {
    try {
      final behaviorData = {
        'action': action,
        'knowledge_id': knowledgeId,
        'question_id': questionId,
        'timestamp': DateTime.now().toIso8601String(),
        'additional_data': additionalData,
      };

      await _apiService.post(
        '/api/explore/behavior',
        body: behaviorData,
        context: context,
      );
    } catch (e) {
      _logger.warning('记录学习行为失败: $e');
      // 不抛出异常，避免影响主要功能
    }
  }

  /// 获取默认知识卡片（离线模式或API失败时使用）
  KnowledgeCard _getDefaultKnowledge() {
    return KnowledgeCard(
      id: 'default_001',
      questionTitle: '什么是深度学习？',
      content: '''深度学习是机器学习的一个分支，它模仿人脑神经网络的工作方式来处理数据。

深度学习的核心特点：
• 多层神经网络结构
• 自动特征提取能力
• 在大数据上表现优异
• 广泛应用于图像识别、自然语言处理等领域

深度学习已经在许多领域取得了突破性进展，包括计算机视觉、语音识别、机器翻译等。它的强大之处在于能够从原始数据中自动学习复杂的特征表示。''',
      createdAt: DateTime.now(),
      sourceType: 'database',
    );
  }

  /// 获取默认关联问题（离线模式或API失败时使用）
  List<RelatedQuestion> _getDefaultRelatedQuestions() {
    return [
      RelatedQuestion(
        id: 'related_001',
        question: '神经网络是如何工作的？',
        description: '了解神经网络的基本原理',
        type: QuestionType.related,
        priority: 1,
      ),
      RelatedQuestion(
        id: 'next_001',
        question: '深度学习的应用领域',
        description: '探索深度学习的实际应用',
        type: QuestionType.next,
        priority: 2,
      ),
      RelatedQuestion(
        id: 'deeper_001',
        question: '如何开始学习深度学习？',
        description: '制定学习计划和路径',
        type: QuestionType.deeper,
        priority: 3,
      ),
    ];
  }

  /// 释放资源
  void dispose() {
    // 如果有需要清理的资源，在这里处理
  }
}
