import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import '../models/explore_model.dart';
import '../constants/fonts.dart';
import '../constants/colors.dart';

/// 关联问题导航组件
/// 包含三个横向排列的卡片：左侧相关问题、中间下一步、右侧深入探索
class RelatedQuestionsNavigation extends StatefulWidget {
  final List<RelatedQuestion> questions;
  final Function(RelatedQuestion) onQuestionTap;
  final bool isLoading;

  const RelatedQuestionsNavigation({
    super.key,
    required this.questions,
    required this.onQuestionTap,
    this.isLoading = false,
  });

  @override
  State<RelatedQuestionsNavigation> createState() => _RelatedQuestionsNavigationState();
}

class _RelatedQuestionsNavigationState extends State<RelatedQuestionsNavigation> {
  @override
  Widget build(BuildContext context) {
    if (widget.isLoading) {
      return _buildLoadingState();
    }

    // 确保有3个问题，不足的用默认问题填充
    final displayQuestions = _prepareDisplayQuestions();

    return Container(
      height: 120,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          // 左侧卡片 - 相关问题
          Expanded(
            child: _buildQuestionCard(
              displayQuestions[0],
              QuestionCardType.left,
            ),
          ),
          
          const SizedBox(width: 12),
          
          // 中间卡片 - 下一步
          Expanded(
            child: _buildQuestionCard(
              displayQuestions[1],
              QuestionCardType.center,
            ),
          ),
          
          const SizedBox(width: 12),
          
          // 右侧卡片 - 深入探索
          Expanded(
            child: _buildQuestionCard(
              displayQuestions[2],
              QuestionCardType.right,
            ),
          ),
        ],
      ),
    );
  }

  /// 准备显示的问题列表
  List<RelatedQuestion> _prepareDisplayQuestions() {
    final questions = List<RelatedQuestion>.from(widget.questions);
    
    // 按类型排序：相关问题、下一步、深入探索
    questions.sort((a, b) {
      final typeOrder = {
        QuestionType.related: 0,
        QuestionType.next: 1,
        QuestionType.deeper: 2,
        QuestionType.practice: 3,
      };
      return (typeOrder[a.type] ?? 99).compareTo(typeOrder[b.type] ?? 99);
    });

    // 确保有3个问题
    while (questions.length < 3) {
      questions.add(_createDefaultQuestion(questions.length));
    }

    return questions.take(3).toList();
  }

  /// 创建默认问题
  RelatedQuestion _createDefaultQuestion(int index) {
    final defaultQuestions = [
      RelatedQuestion(
        id: 'default_related',
        question: '相关概念',
        description: '探索相关的学习内容',
        type: QuestionType.related,
      ),
      RelatedQuestion(
        id: 'default_next',
        question: '下一步学习',
        description: '继续深入学习',
        type: QuestionType.next,
      ),
      RelatedQuestion(
        id: 'default_deeper',
        question: '深入了解',
        description: '获取更多详细信息',
        type: QuestionType.deeper,
      ),
    ];
    
    return defaultQuestions[index % defaultQuestions.length];
  }

  /// 构建问题卡片
  Widget _buildQuestionCard(RelatedQuestion question, QuestionCardType cardType) {
    return GestureDetector(
      onTap: () => widget.onQuestionTap(question),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(16),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
            child: Container(
              decoration: BoxDecoration(
                gradient: _getCardGradient(cardType),
                border: Border.all(
                  color: _getCardBorderColor(cardType),
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // 顶部图标和类型
                    Row(
                      children: [
                        Icon(
                          _getCardIcon(cardType),
                          color: _getCardIconColor(cardType),
                          size: 20,
                        ),
                        const SizedBox(width: 6),
                        Expanded(
                          child: Text(
                            _getCardTypeText(cardType),
                            style: TextStyle(
                              color: _getCardIconColor(cardType),
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ),
                    
                    // 问题文本
                    Expanded(
                      child: Center(
                        child: Text(
                          question.question,
                          style: AppFonts.createMixedStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                            height: 1.3,
                          ),
                          textAlign: TextAlign.center,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ),
                    
                    // 底部指示器
                    Center(
                      child: Container(
                        width: 24,
                        height: 3,
                        decoration: BoxDecoration(
                          color: _getCardIconColor(cardType).withValues(alpha: 0.6),
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建加载状态
  Widget _buildLoadingState() {
    return Container(
      height: 120,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: List.generate(3, (index) => Expanded(
          child: Container(
            margin: EdgeInsets.only(right: index < 2 ? 12 : 0),
            decoration: BoxDecoration(
              color: AppColors.darkGray,
              borderRadius: BorderRadius.circular(16),
            ),
            child: const Center(
              child: CupertinoActivityIndicator(
                radius: 12,
                color: Colors.white,
              ),
            ),
          ),
        )),
      ),
    );
  }

  /// 获取卡片渐变
  LinearGradient _getCardGradient(QuestionCardType cardType) {
    switch (cardType) {
      case QuestionCardType.left:
        return LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.blue.withValues(alpha: 0.2),
            Colors.blue.withValues(alpha: 0.1),
          ],
        );
      case QuestionCardType.center:
        return LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.green.withValues(alpha: 0.2),
            Colors.green.withValues(alpha: 0.1),
          ],
        );
      case QuestionCardType.right:
        return LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.purple.withValues(alpha: 0.2),
            Colors.purple.withValues(alpha: 0.1),
          ],
        );
    }
  }

  /// 获取卡片边框颜色
  Color _getCardBorderColor(QuestionCardType cardType) {
    switch (cardType) {
      case QuestionCardType.left:
        return Colors.blue.withValues(alpha: 0.3);
      case QuestionCardType.center:
        return Colors.green.withValues(alpha: 0.3);
      case QuestionCardType.right:
        return Colors.purple.withValues(alpha: 0.3);
    }
  }

  /// 获取卡片图标
  IconData _getCardIcon(QuestionCardType cardType) {
    switch (cardType) {
      case QuestionCardType.left:
        return CupertinoIcons.link;
      case QuestionCardType.center:
        return CupertinoIcons.arrow_right_circle;
      case QuestionCardType.right:
        return CupertinoIcons.search_circle;
    }
  }

  /// 获取卡片图标颜色
  Color _getCardIconColor(QuestionCardType cardType) {
    switch (cardType) {
      case QuestionCardType.left:
        return Colors.blue;
      case QuestionCardType.center:
        return Colors.green;
      case QuestionCardType.right:
        return Colors.purple;
    }
  }

  /// 获取卡片类型文本
  String _getCardTypeText(QuestionCardType cardType) {
    switch (cardType) {
      case QuestionCardType.left:
        return '相关';
      case QuestionCardType.center:
        return '下一步';
      case QuestionCardType.right:
        return '深入';
    }
  }
}

/// 问题卡片类型枚举
enum QuestionCardType {
  left,    // 左侧卡片
  center,  // 中间卡片
  right,   // 右侧卡片
}

/// 关联问题导航骨架加载组件
class RelatedQuestionsNavigationSkeleton extends StatelessWidget {
  const RelatedQuestionsNavigationSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 120,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: List.generate(3, (index) => Expanded(
          child: Container(
            margin: EdgeInsets.only(right: index < 2 ? 12 : 0),
            decoration: BoxDecoration(
              color: AppColors.darkGray,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 顶部骨架
                  Row(
                    children: [
                      Container(
                        width: 20,
                        height: 20,
                        decoration: BoxDecoration(
                          color: AppColors.mediumGray,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                      const SizedBox(width: 6),
                      Expanded(
                        child: Container(
                          height: 12,
                          decoration: BoxDecoration(
                            color: AppColors.mediumGray,
                            borderRadius: BorderRadius.circular(2),
                          ),
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // 中间内容骨架
                  Expanded(
                    child: Center(
                      child: Container(
                        height: 16,
                        width: double.infinity,
                        decoration: BoxDecoration(
                          color: AppColors.mediumGray,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // 底部指示器骨架
                  Center(
                    child: Container(
                      width: 24,
                      height: 3,
                      decoration: BoxDecoration(
                        color: AppColors.mediumGray,
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        )),
      ),
    );
  }
}
