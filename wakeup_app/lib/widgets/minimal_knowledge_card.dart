import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:mesh/mesh.dart';
import '../models/explore_model.dart';
import '../constants/fonts.dart';

/// 轻量简约的知识卡片组件
/// Apple暗夜深邃风格，使用网格渐变
class MinimalKnowledgeCard extends StatefulWidget {
  final KnowledgeCard knowledge;
  final VoidCallback? onTap;
  final bool isLoading;

  const MinimalKnowledgeCard({
    super.key,
    required this.knowledge,
    this.onTap,
    this.isLoading = false,
  });

  @override
  State<MinimalKnowledgeCard> createState() => _MinimalKnowledgeCardState();
}

class _MinimalKnowledgeCardState extends State<MinimalKnowledgeCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.995).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// 获取深邃的网格渐变
  OMeshRect get _deepMeshGradient {
    return OMeshRect(
      width: 4,
      height: 4,
      colorSpace: OMeshColorSpace.lab,
      fallbackColor: const Color(0xFF000000),
      backgroundColor: const Color(0xFF000000),
      vertices: [
        // 第一行 - 深邃夜空
        (-0.1, -0.1).v,
        (0.4, -0.05).v,
        (0.6, -0.05).v,
        (1.1, -0.1).v,

        // 第二行 - 微光渗透
        (-0.05, 0.3).v,
        (0.35, 0.25).v.bezier(east: (0.45, 0.22).v, west: (0.25, 0.28).v),
        (0.65, 0.25).v.bezier(east: (0.75, 0.22).v, west: (0.55, 0.28).v),
        (1.05, 0.3).v,

        // 第三行 - 深度扩散
        (-0.05, 0.7).v,
        (0.35, 0.75).v.bezier(east: (0.45, 0.78).v, west: (0.25, 0.72).v),
        (0.65, 0.75).v.bezier(east: (0.75, 0.78).v, west: (0.55, 0.72).v),
        (1.05, 0.7).v,

        // 第四行 - 边缘消散
        (-0.1, 1.1).v,
        (0.4, 1.05).v,
        (0.6, 1.05).v,
        (1.1, 1.1).v,
      ],
      colors: [
        // 第一行 - 深邃夜空
        const Color(0xFF000000),
        const Color(0xFF0A0A0A),
        const Color(0xFF0A0A0A),
        const Color(0xFF000000),

        // 第二行 - 微光渗透
        const Color(0xFF050505),
        const Color(0xFF1A1A1A),
        const Color(0xFF1A1A1A),
        const Color(0xFF050505),

        // 第三行 - 深度扩散
        const Color(0xFF050505),
        const Color(0xFF151515),
        const Color(0xFF151515),
        const Color(0xFF050505),

        // 第四行 - 边缘消散
        const Color(0xFF000000),
        const Color(0xFF0A0A0A),
        const Color(0xFF0A0A0A),
        const Color(0xFF000000),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    if (widget.isLoading) {
      return _buildLoadingCard();
    }

    return GestureDetector(
      onTapDown: (_) => _animationController.forward(),
      onTapUp: (_) {
        _animationController.reverse();
        widget.onTap?.call();
      },
      onTapCancel: () => _animationController.reverse(),
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 20),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(32),
                child: Stack(
                  children: [
                    // 深邃网格渐变背景
                    SizedBox(
                      height: 320,
                      child: OMeshGradient(
                        size: Size.infinite,
                        tessellation: 20,
                        mesh: _deepMeshGradient,
                      ),
                    ),

                    // 微妙的径向光效
                    Container(
                      height: 320,
                      decoration: const BoxDecoration(
                        gradient: RadialGradient(
                          center: Alignment(0.3, -0.4),
                          radius: 1.2,
                          colors: [
                            Color(0x08FFFFFF),
                            Color(0x04FFFFFF),
                            Color(0x02FFFFFF),
                            Colors.transparent,
                          ],
                          stops: [0.0, 0.3, 0.6, 1.0],
                        ),
                      ),
                    ),

                    // 内容区域
                    Container(
                      height: 320,
                      padding: const EdgeInsets.all(28),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // 问题标题
                          Text(
                            widget.knowledge.questionTitle,
                            style: AppFonts.createMixedStyle(
                              fontSize: 22,
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                              height: 1.3,
                              isTitle: true,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),

                          const SizedBox(height: 20),

                          // 内容文本
                          Expanded(
                            child: SingleChildScrollView(
                              physics: const BouncingScrollPhysics(),
                              child: Text(
                                widget.knowledge.content,
                                style: AppFonts.createMixedStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w400,
                                  color: Colors.white.withValues(alpha: 0.85),
                                  height: 1.6,
                                  letterSpacing: 0.2,
                                ),
                              ),
                            ),
                          ),

                          const SizedBox(height: 16),

                          // 底部信息
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              // 来源标识
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 10,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: _getSourceColor().withValues(
                                    alpha: 0.15,
                                  ),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      _getSourceIcon(),
                                      size: 12,
                                      color: _getSourceColor(),
                                    ),
                                    const SizedBox(width: 4),
                                    Text(
                                      _getSourceText(),
                                      style: TextStyle(
                                        color: _getSourceColor(),
                                        fontSize: 11,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ],
                                ),
                              ),

                              // 时间信息
                              Text(
                                _formatTime(widget.knowledge.createdAt),
                                style: TextStyle(
                                  color: Colors.white.withValues(alpha: 0.4),
                                  fontSize: 11,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  /// 构建加载状态卡片
  Widget _buildLoadingCard() {
    return Container(
      height: 320,
      margin: const EdgeInsets.symmetric(horizontal: 20),
      decoration: BoxDecoration(
        color: const Color(0xFF0A0A0A),
        borderRadius: BorderRadius.circular(32),
      ),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CupertinoActivityIndicator(radius: 16, color: Colors.white),
            SizedBox(height: 16),
            Text(
              'AI正在思考中...',
              style: TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w400,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 获取来源颜色
  Color _getSourceColor() {
    switch (widget.knowledge.sourceType) {
      case 'ai_generated':
        return const Color(0xFF8B5CF6);
      case 'database':
        return const Color(0xFF10B981);
      default:
        return const Color(0xFF6B7280);
    }
  }

  /// 获取来源图标
  IconData _getSourceIcon() {
    switch (widget.knowledge.sourceType) {
      case 'ai_generated':
        return CupertinoIcons.sparkles;
      case 'database':
        return CupertinoIcons.book;
      default:
        return CupertinoIcons.info;
    }
  }

  /// 获取来源文本
  String _getSourceText() {
    switch (widget.knowledge.sourceType) {
      case 'ai_generated':
        return 'AI生成';
      case 'database':
        return '知识库';
      default:
        return '未知';
    }
  }

  /// 格式化时间显示
  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return '刚刚';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}分钟前';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}小时前';
    } else {
      return '${dateTime.month}月${dateTime.day}日';
    }
  }
}
