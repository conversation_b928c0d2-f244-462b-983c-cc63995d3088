import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import '../models/explore_model.dart';
import '../constants/fonts.dart';

/// 轻量简约的关联问题导航组件
/// Apple暗夜深邃风格
class MinimalRelatedQuestions extends StatefulWidget {
  final List<RelatedQuestion> questions;
  final Function(RelatedQuestion) onQuestionTap;
  final bool isLoading;

  const MinimalRelatedQuestions({
    super.key,
    required this.questions,
    required this.onQuestionTap,
    this.isLoading = false,
  });

  @override
  State<MinimalRelatedQuestions> createState() => _MinimalRelatedQuestionsState();
}

class _MinimalRelatedQuestionsState extends State<MinimalRelatedQuestions> {
  @override
  Widget build(BuildContext context) {
    if (widget.isLoading) {
      return _buildLoadingState();
    }

    final displayQuestions = _prepareDisplayQuestions();

    return Container(
      height: 80,
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        children: [
          // 左侧问题
          Expanded(
            child: _buildQuestionChip(
              displayQuestions[0],
              const Color(0xFF3B82F6),
              CupertinoIcons.link,
            ),
          ),
          
          const SizedBox(width: 12),
          
          // 中间问题
          Expanded(
            child: _buildQuestionChip(
              displayQuestions[1],
              const Color(0xFF10B981),
              CupertinoIcons.arrow_right_circle,
            ),
          ),
          
          const SizedBox(width: 12),
          
          // 右侧问题
          Expanded(
            child: _buildQuestionChip(
              displayQuestions[2],
              const Color(0xFF8B5CF6),
              CupertinoIcons.search_circle,
            ),
          ),
        ],
      ),
    );
  }

  /// 准备显示的问题列表
  List<RelatedQuestion> _prepareDisplayQuestions() {
    final questions = List<RelatedQuestion>.from(widget.questions);
    
    // 按类型排序
    questions.sort((a, b) {
      final typeOrder = {
        QuestionType.related: 0,
        QuestionType.next: 1,
        QuestionType.deeper: 2,
        QuestionType.practice: 3,
      };
      return (typeOrder[a.type] ?? 99).compareTo(typeOrder[b.type] ?? 99);
    });

    // 确保有3个问题
    while (questions.length < 3) {
      questions.add(_createDefaultQuestion(questions.length));
    }

    return questions.take(3).toList();
  }

  /// 创建默认问题
  RelatedQuestion _createDefaultQuestion(int index) {
    final defaultQuestions = [
      RelatedQuestion(
        id: 'default_related',
        question: '相关概念',
        description: '探索相关的学习内容',
        type: QuestionType.related,
      ),
      RelatedQuestion(
        id: 'default_next',
        question: '下一步学习',
        description: '继续深入学习',
        type: QuestionType.next,
      ),
      RelatedQuestion(
        id: 'default_deeper',
        question: '深入了解',
        description: '获取更多详细信息',
        type: QuestionType.deeper,
      ),
    ];
    
    return defaultQuestions[index % defaultQuestions.length];
  }

  /// 构建问题芯片
  Widget _buildQuestionChip(RelatedQuestion question, Color accentColor, IconData icon) {
    return GestureDetector(
      onTap: () => widget.onQuestionTap(question),
      child: Container(
        height: 80,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.08),
            width: 0.5,
          ),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(20),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
            child: Container(
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Stack(
                children: [
                  // 微妙的渐变光效
                  Container(
                    decoration: BoxDecoration(
                      gradient: RadialGradient(
                        center: const Alignment(0.8, -0.6),
                        radius: 1.5,
                        colors: [
                          accentColor.withValues(alpha: 0.08),
                          accentColor.withValues(alpha: 0.04),
                          Colors.transparent,
                        ],
                        stops: const [0.0, 0.5, 1.0],
                      ),
                      borderRadius: BorderRadius.circular(20),
                    ),
                  ),

                  // 内容
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // 图标
                        Icon(
                          icon,
                          color: accentColor.withValues(alpha: 0.8),
                          size: 18,
                        ),
                        
                        // 问题文本
                        Expanded(
                          child: Center(
                            child: Text(
                              question.question,
                              style: AppFonts.createMixedStyle(
                                fontSize: 13,
                                fontWeight: FontWeight.w500,
                                color: Colors.white.withValues(alpha: 0.9),
                                height: 1.2,
                              ),
                              textAlign: TextAlign.center,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建加载状态
  Widget _buildLoadingState() {
    return Container(
      height: 80,
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        children: List.generate(3, (index) => Expanded(
          child: Container(
            margin: EdgeInsets.only(right: index < 2 ? 12 : 0),
            decoration: BoxDecoration(
              color: const Color(0xFF0A0A0A),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.05),
                width: 0.5,
              ),
            ),
            child: const Center(
              child: CupertinoActivityIndicator(
                radius: 10,
                color: Colors.white,
              ),
            ),
          ),
        )),
      ),
    );
  }
}

/// 关联问题导航骨架加载组件
class MinimalRelatedQuestionsSkeleton extends StatelessWidget {
  const MinimalRelatedQuestionsSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 80,
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        children: List.generate(3, (index) => Expanded(
          child: Container(
            margin: EdgeInsets.only(right: index < 2 ? 12 : 0),
            decoration: BoxDecoration(
              color: const Color(0xFF0A0A0A),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.05),
                width: 0.5,
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 图标骨架
                  Container(
                    width: 18,
                    height: 18,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                  
                  const Spacer(),
                  
                  // 文本骨架
                  Container(
                    height: 12,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(6),
                    ),
                  ),
                ],
              ),
            ),
          ),
        )),
      ),
    );
  }
}
