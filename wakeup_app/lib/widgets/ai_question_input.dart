import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import '../constants/fonts.dart';
import '../constants/colors.dart';

/// AI提问交互组件
/// 底部输入框，支持用户提问和AI回答的完整交互流程
class AiQuestionInput extends StatefulWidget {
  final Function(String) onQuestionSubmitted;
  final bool isLoading;
  final String? placeholder;
  final VoidCallback? onMicTap;
  final bool showMicButton;

  const AiQuestionInput({
    super.key,
    required this.onQuestionSubmitted,
    this.isLoading = false,
    this.placeholder,
    this.onMicTap,
    this.showMicButton = true,
  });

  @override
  State<AiQuestionInput> createState() => _AiQuestionInputState();
}

class _AiQuestionInputState extends State<AiQuestionInput>
    with SingleTickerProviderStateMixin {
  late TextEditingController _textController;
  late FocusNode _focusNode;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<Color?> _colorAnimation;

  bool _hasText = false;

  @override
  void initState() {
    super.initState();
    _textController = TextEditingController();
    _focusNode = FocusNode();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _colorAnimation = ColorTween(
      begin: Colors.blue.withValues(alpha: 0.6),
      end: Colors.blue,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _textController.addListener(_onTextChanged);
    _focusNode.addListener(_onFocusChanged);
  }

  @override
  void dispose() {
    _textController.dispose();
    _focusNode.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    final hasText = _textController.text.trim().isNotEmpty;
    if (hasText != _hasText) {
      setState(() {
        _hasText = hasText;
      });
      
      if (hasText) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    }
  }

  void _onFocusChanged() {
    setState(() {});
  }

  void _onSubmit() {
    final question = _textController.text.trim();
    if (question.isNotEmpty && !widget.isLoading) {
      widget.onQuestionSubmitted(question);
      _textController.clear();
      _focusNode.unfocus();
    }
  }

  @override
  Widget build(BuildContext context) {
    final bottomPadding = MediaQuery.of(context).padding.bottom;
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    
    return Container(
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.3),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.2),
            blurRadius: 20,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: ClipRect(
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
          child: Container(
            padding: EdgeInsets.only(
              left: 16,
              right: 16,
              top: 16,
              bottom: 16 + (keyboardHeight > 0 ? 8 : bottomPadding),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 提示文本（仅在键盘弹起时显示）
                if (keyboardHeight > 0) _buildHintText(),
                
                // 输入框区域
                _buildInputArea(),
                
                // 快捷问题建议（仅在无键盘时显示）
                if (keyboardHeight == 0) _buildQuickSuggestions(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建提示文本
  Widget _buildHintText() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Text(
        '💡 试试问一些具体的问题，AI会给你详细的解答',
        style: AppFonts.createMixedStyle(
          fontSize: 13,
          color: Colors.white.withValues(alpha: 0.7),
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  /// 构建输入框区域
  Widget _buildInputArea() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(24),
        border: Border.all(
          color: _focusNode.hasFocus 
              ? Colors.blue.withValues(alpha: 0.6)
              : Colors.white.withValues(alpha: 0.2),
          width: 1.5,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(24),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(24),
            ),
            child: Row(
              children: [
                // 语音输入按钮
                if (widget.showMicButton) _buildMicButton(),
                
                // 文本输入框
                Expanded(child: _buildTextField()),
                
                // 发送按钮
                _buildSendButton(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建语音输入按钮
  Widget _buildMicButton() {
    return GestureDetector(
      onTap: widget.onMicTap,
      child: Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(24),
        ),
        child: Icon(
          CupertinoIcons.mic,
          color: Colors.white.withValues(alpha: 0.8),
          size: 20,
        ),
      ),
    );
  }

  /// 构建文本输入框
  Widget _buildTextField() {
    return TextField(
      controller: _textController,
      focusNode: _focusNode,
      enabled: !widget.isLoading,
      maxLines: null,
      minLines: 1,
      style: AppFonts.createMixedStyle(
        fontSize: 16,
        color: Colors.white,
      ),
      decoration: InputDecoration(
        hintText: widget.placeholder ?? '问我任何问题...',
        hintStyle: AppFonts.createMixedStyle(
          fontSize: 16,
          color: Colors.white.withValues(alpha: 0.5),
        ),
        border: InputBorder.none,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 14,
        ),
      ),
      textInputAction: TextInputAction.send,
      onSubmitted: (_) => _onSubmit(),
    );
  }

  /// 构建发送按钮
  Widget _buildSendButton() {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return GestureDetector(
          onTap: widget.isLoading ? null : _onSubmit,
          child: Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              width: 48,
              height: 48,
              margin: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: widget.isLoading
                    ? Colors.grey.withValues(alpha: 0.3)
                    : (_hasText 
                        ? _colorAnimation.value 
                        : Colors.white.withValues(alpha: 0.2)),
                borderRadius: BorderRadius.circular(22),
              ),
              child: widget.isLoading
                  ? const CupertinoActivityIndicator(
                      radius: 10,
                      color: Colors.white,
                    )
                  : Icon(
                      CupertinoIcons.arrow_up,
                      color: Colors.white,
                      size: 20,
                    ),
            ),
          ),
        );
      },
    );
  }

  /// 构建快捷问题建议
  Widget _buildQuickSuggestions() {
    final suggestions = [
      '这个概念的实际应用是什么？',
      '能举个具体的例子吗？',
      '有什么学习建议？',
    ];

    return Padding(
      padding: const EdgeInsets.only(top: 12),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: suggestions.map((suggestion) => 
            _buildSuggestionChip(suggestion)
          ).toList(),
        ),
      ),
    );
  }

  /// 构建建议芯片
  Widget _buildSuggestionChip(String text) {
    return GestureDetector(
      onTap: () {
        if (!widget.isLoading) {
          _textController.text = text;
          _onSubmit();
        }
      },
      child: Container(
        margin: const EdgeInsets.only(right: 8),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        child: Text(
          text,
          style: AppFonts.createMixedStyle(
            fontSize: 13,
            color: Colors.white.withValues(alpha: 0.8),
          ),
        ),
      ),
    );
  }
}

/// AI提问输入框骨架加载组件
class AiQuestionInputSkeleton extends StatelessWidget {
  const AiQuestionInputSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    final bottomPadding = MediaQuery.of(context).padding.bottom;
    
    return Container(
      decoration: BoxDecoration(
        color: AppColors.darkGray,
      ),
      child: Padding(
        padding: EdgeInsets.only(
          left: 16,
          right: 16,
          top: 16,
          bottom: 16 + bottomPadding,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 输入框骨架
            Container(
              height: 48,
              decoration: BoxDecoration(
                color: AppColors.mediumGray,
                borderRadius: BorderRadius.circular(24),
              ),
            ),
            
            const SizedBox(height: 12),
            
            // 建议芯片骨架
            Row(
              children: List.generate(3, (index) => Expanded(
                child: Container(
                  height: 32,
                  margin: EdgeInsets.only(right: index < 2 ? 8 : 0),
                  decoration: BoxDecoration(
                    color: AppColors.mediumGray,
                    borderRadius: BorderRadius.circular(16),
                  ),
                ),
              )),
            ),
          ],
        ),
      ),
    );
  }
}
