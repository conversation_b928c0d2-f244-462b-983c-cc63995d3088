import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import '../models/explore_model.dart';
import '../constants/fonts.dart';
import '../constants/colors.dart';

/// 主知识卡片组件
/// 用于展示学习知识的问题标题和正文内容
class KnowledgeCardWidget extends StatefulWidget {
  final KnowledgeCard knowledge;
  final VoidCallback? onTap;
  final bool isLoading;
  final double? height;

  const KnowledgeCardWidget({
    super.key,
    required this.knowledge,
    this.onTap,
    this.isLoading = false,
    this.height,
  });

  @override
  State<KnowledgeCardWidget> createState() => _KnowledgeCardWidgetState();
}

class _KnowledgeCardWidgetState extends State<KnowledgeCardWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.98).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _opacityAnimation = Tween<double>(begin: 1.0, end: 0.9).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final cardHeight = widget.height ?? screenHeight * 0.45; // 占屏幕45%高度

    return GestureDetector(
      onTapDown: (_) => _animationController.forward(),
      onTapUp: (_) {
        _animationController.reverse();
        widget.onTap?.call();
      },
      onTapCancel: () => _animationController.reverse(),
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Opacity(
              opacity: _opacityAnimation.value,
              child: Container(
                height: cardHeight,
                margin: const EdgeInsets.symmetric(horizontal: 16),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(24),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.3),
                      blurRadius: 20,
                      offset: const Offset(0, 8),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(24),
                  child: BackdropFilter(
                    filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            Colors.white.withValues(alpha: 0.1),
                            Colors.white.withValues(alpha: 0.05),
                          ],
                        ),
                        border: Border.all(
                          color: Colors.white.withValues(alpha: 0.2),
                          width: 1,
                        ),
                        borderRadius: BorderRadius.circular(24),
                      ),
                      child:
                          widget.isLoading
                              ? _buildLoadingContent()
                              : _buildKnowledgeContent(),
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  /// 构建加载状态内容
  Widget _buildLoadingContent() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CupertinoActivityIndicator(radius: 20, color: Colors.white),
          SizedBox(height: 16),
          Text(
            'AI正在思考中...',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建知识内容
  Widget _buildKnowledgeContent() {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 问题标题区域
          _buildQuestionTitle(),

          const SizedBox(height: 20),

          // 分割线
          Container(
            height: 1,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.transparent,
                  Colors.white.withValues(alpha: 0.3),
                  Colors.transparent,
                ],
              ),
            ),
          ),

          const SizedBox(height: 20),

          // 正文内容区域
          Expanded(child: _buildContentArea()),

          // 底部信息
          _buildBottomInfo(),
        ],
      ),
    );
  }

  /// 构建问题标题
  Widget _buildQuestionTitle() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 问题图标
        Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            color: Colors.blue.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(
            CupertinoIcons.question_circle,
            color: Colors.blue,
            size: 20,
          ),
        ),

        const SizedBox(width: 12),

        // 标题文本
        Expanded(
          child: Text(
            widget.knowledge.questionTitle,
            style: AppFonts.createMixedStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.white,
              height: 1.3,
              isTitle: true,
            ),
          ),
        ),
      ],
    );
  }

  /// 构建内容区域
  Widget _buildContentArea() {
    return SingleChildScrollView(
      physics: const BouncingScrollPhysics(),
      child: Text(
        widget.knowledge.content,
        style: AppFonts.createMixedStyle(
          fontSize: 16,
          fontWeight: FontWeight.w400,
          color: Colors.white.withValues(alpha: 0.9),
          height: 1.6,
          letterSpacing: 0.3,
        ),
      ),
    );
  }

  /// 构建底部信息
  Widget _buildBottomInfo() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // 来源标识
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: _getSourceColor().withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(_getSourceIcon(), size: 14, color: _getSourceColor()),
              const SizedBox(width: 4),
              Text(
                _getSourceText(),
                style: TextStyle(
                  color: _getSourceColor(),
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),

        // 时间信息
        Text(
          _formatTime(widget.knowledge.createdAt),
          style: TextStyle(
            color: Colors.white.withValues(alpha: 0.6),
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  /// 获取来源颜色
  Color _getSourceColor() {
    switch (widget.knowledge.sourceType) {
      case 'ai_generated':
        return Colors.purple;
      case 'database':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  /// 获取来源图标
  IconData _getSourceIcon() {
    switch (widget.knowledge.sourceType) {
      case 'ai_generated':
        return CupertinoIcons.sparkles;
      case 'database':
        return CupertinoIcons.book;
      default:
        return CupertinoIcons.info;
    }
  }

  /// 获取来源文本
  String _getSourceText() {
    switch (widget.knowledge.sourceType) {
      case 'ai_generated':
        return 'AI生成';
      case 'database':
        return '知识库';
      default:
        return '未知';
    }
  }

  /// 格式化时间显示
  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return '刚刚';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}分钟前';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}小时前';
    } else {
      return '${dateTime.month}月${dateTime.day}日';
    }
  }
}

/// 知识卡片骨架加载组件
class KnowledgeCardWidgetSkeleton extends StatelessWidget {
  final double? height;

  const KnowledgeCardWidgetSkeleton({super.key, this.height});

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final cardHeight = height ?? screenHeight * 0.45;

    return Container(
      height: cardHeight,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: AppColors.darkGray,
        borderRadius: BorderRadius.circular(24),
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题骨架
            Row(
              children: [
                Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: AppColors.mediumGray,
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Container(
                    height: 24,
                    decoration: BoxDecoration(
                      color: AppColors.mediumGray,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 20),

            // 分割线
            Container(height: 1, color: AppColors.mediumGray),

            const SizedBox(height: 20),

            // 内容骨架
            Expanded(
              child: Column(
                children: List.generate(
                  6,
                  (index) => Padding(
                    padding: const EdgeInsets.only(bottom: 12),
                    child: Container(
                      height: 16,
                      width: index == 5 ? 200 : double.infinity,
                      decoration: BoxDecoration(
                        color: AppColors.mediumGray,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
