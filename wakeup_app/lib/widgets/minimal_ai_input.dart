import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import '../constants/fonts.dart';

/// 轻量简约的AI提问输入组件
/// Apple暗夜深邃风格
class MinimalAiInput extends StatefulWidget {
  final Function(String) onQuestionSubmitted;
  final bool isLoading;
  final String? placeholder;

  const MinimalAiInput({
    super.key,
    required this.onQuestionSubmitted,
    this.isLoading = false,
    this.placeholder,
  });

  @override
  State<MinimalAiInput> createState() => _MinimalAiInputState();
}

class _MinimalAiInputState extends State<MinimalAiInput>
    with SingleTickerProviderStateMixin {
  late TextEditingController _textController;
  late FocusNode _focusNode;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  bool _hasText = false;

  @override
  void initState() {
    super.initState();
    _textController = TextEditingController();
    _focusNode = FocusNode();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.02,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _textController.addListener(_onTextChanged);
    _focusNode.addListener(_onFocusChanged);
  }

  @override
  void dispose() {
    _textController.dispose();
    _focusNode.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    final hasText = _textController.text.trim().isNotEmpty;
    if (hasText != _hasText) {
      setState(() {
        _hasText = hasText;
      });
      
      if (hasText) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    }
  }

  void _onFocusChanged() {
    setState(() {});
  }

  void _onSubmit() {
    final question = _textController.text.trim();
    if (question.isNotEmpty && !widget.isLoading) {
      widget.onQuestionSubmitted(question);
      _textController.clear();
      _focusNode.unfocus();
    }
  }

  @override
  Widget build(BuildContext context) {
    final bottomPadding = MediaQuery.of(context).padding.bottom;
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    
    return Container(
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.8),
      ),
      child: ClipRect(
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 40, sigmaY: 40),
          child: Container(
            padding: EdgeInsets.only(
              left: 20,
              right: 20,
              top: 20,
              bottom: 20 + (keyboardHeight > 0 ? 8 : bottomPadding),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 快捷问题建议（仅在无键盘时显示）
                if (keyboardHeight == 0) _buildQuickSuggestions(),
                
                // 输入框区域
                _buildInputArea(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建输入框区域
  Widget _buildInputArea() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(28),
        border: Border.all(
          color: _focusNode.hasFocus 
              ? const Color(0xFF3B82F6).withValues(alpha: 0.3)
              : Colors.white.withValues(alpha: 0.08),
          width: 1,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(28),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.4),
              borderRadius: BorderRadius.circular(28),
            ),
            child: Row(
              children: [
                // 文本输入框
                Expanded(child: _buildTextField()),
                
                // 发送按钮
                _buildSendButton(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建文本输入框
  Widget _buildTextField() {
    return TextField(
      controller: _textController,
      focusNode: _focusNode,
      enabled: !widget.isLoading,
      maxLines: null,
      minLines: 1,
      style: AppFonts.createMixedStyle(
        fontSize: 16,
        color: Colors.white,
        fontWeight: FontWeight.w400,
      ),
      decoration: InputDecoration(
        hintText: widget.placeholder ?? '问我任何问题...',
        hintStyle: AppFonts.createMixedStyle(
          fontSize: 16,
          color: Colors.white.withValues(alpha: 0.4),
          fontWeight: FontWeight.w400,
        ),
        border: InputBorder.none,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 20,
          vertical: 16,
        ),
      ),
      textInputAction: TextInputAction.send,
      onSubmitted: (_) => _onSubmit(),
    );
  }

  /// 构建发送按钮
  Widget _buildSendButton() {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return GestureDetector(
          onTap: widget.isLoading ? null : _onSubmit,
          child: Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              width: 44,
              height: 44,
              margin: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: widget.isLoading
                    ? Colors.grey.withValues(alpha: 0.2)
                    : (_hasText 
                        ? const Color(0xFF3B82F6)
                        : Colors.white.withValues(alpha: 0.1)),
                borderRadius: BorderRadius.circular(22),
              ),
              child: widget.isLoading
                  ? const CupertinoActivityIndicator(
                      radius: 8,
                      color: Colors.white,
                    )
                  : Icon(
                      CupertinoIcons.arrow_up,
                      color: Colors.white.withValues(
                        alpha: _hasText ? 1.0 : 0.6,
                      ),
                      size: 18,
                    ),
            ),
          ),
        );
      },
    );
  }

  /// 构建快捷问题建议
  Widget _buildQuickSuggestions() {
    final suggestions = [
      '这个概念的实际应用？',
      '能举个具体例子吗？',
      '有什么学习建议？',
    ];

    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: suggestions.map((suggestion) => 
            _buildSuggestionChip(suggestion)
          ).toList(),
        ),
      ),
    );
  }

  /// 构建建议芯片
  Widget _buildSuggestionChip(String text) {
    return GestureDetector(
      onTap: () {
        if (!widget.isLoading) {
          _textController.text = text;
          _onSubmit();
        }
      },
      child: Container(
        margin: const EdgeInsets.only(right: 8),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.08),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.1),
            width: 0.5,
          ),
        ),
        child: Text(
          text,
          style: AppFonts.createMixedStyle(
            fontSize: 13,
            color: Colors.white.withValues(alpha: 0.7),
            fontWeight: FontWeight.w400,
          ),
        ),
      ),
    );
  }
}
