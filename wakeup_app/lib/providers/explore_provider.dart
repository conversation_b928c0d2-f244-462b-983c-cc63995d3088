import 'package:flutter/material.dart';
import '../models/explore_model.dart';
import '../services/explore_api_service.dart';
import '../core/utils/logger.dart';

/// 探索页面状态管理Provider
/// 管理知识卡片、关联问题、AI交互等状态
class ExploreProvider extends ChangeNotifier {
  final ExploreApiService _apiService = ExploreApiService();
  final TaggedLogger _logger = const TaggedLogger('ExploreProvider');

  // 状态数据
  ExplorePageState _state = ExplorePageState();

  // Getters
  ExplorePageState get state => _state;
  KnowledgeCard? get currentKnowledge => _state.currentKnowledge;
  List<RelatedQuestion> get relatedQuestions => _state.relatedQuestions;
  bool get isLoading => _state.isLoading;
  String? get error => _state.error;
  String? get currentUserQuestion => _state.currentUserQuestion;

  // 扩展的便捷getter
  bool get hasRelatedQuestions => _state.relatedQuestions.isNotEmpty;
  bool get isInitial =>
      _state.currentKnowledge == null &&
      !_state.isLoading &&
      _state.error == null;
  bool get hasContent => _state.currentKnowledge != null;

  /// 初始化探索页面
  /// 加载初始知识内容和关联问题
  Future<void> initialize(BuildContext? context) async {
    try {
      _updateState(_state.setLoading(true));

      // 并行加载初始知识和关联问题
      final futures = await Future.wait([
        _apiService.getInitialKnowledge(context),
        _apiService.getRelatedQuestions(buildContext: context),
      ]);

      final initialKnowledge = futures[0] as KnowledgeCard?;
      final relatedQuestions = futures[1] as List<RelatedQuestion>;

      _updateState(
        _state.copyWith(
          currentKnowledge: initialKnowledge,
          relatedQuestions: relatedQuestions,
          isLoading: false,
        ),
      );

      _logger.info('探索页面初始化完成');
    } catch (e) {
      _logger.error('探索页面初始化失败', error: e);
      _updateState(_state.copyWith(isLoading: false, error: '初始化失败，请重试'));
    }
  }

  /// 向AI提问
  /// 处理用户输入的问题，获取AI回答并更新界面
  Future<void> askAiQuestion(String question, BuildContext? context) async {
    try {
      _updateState(
        _state.copyWith(
          isLoading: true,
          currentUserQuestion: question,
          error: null,
        ),
      );

      // 记录用户行为
      _apiService.recordLearningBehavior(
        action: 'ask_question',
        additionalData: {'question': question},
        context: context,
      );

      // 获取AI回答
      final aiKnowledge = await _apiService.askAiQuestion(
        question: question,
        context: _state.currentKnowledge?.content,
        buildContext: context,
      );

      if (aiKnowledge != null) {
        // 生成新的关联问题
        final newRelatedQuestions = await _apiService
            .generateAiRelatedQuestions(
              currentContent: aiKnowledge.content,
              questionTitle: aiKnowledge.questionTitle,
              buildContext: context,
            );

        _updateState(
          _state.copyWith(
            currentKnowledge: aiKnowledge,
            relatedQuestions: newRelatedQuestions,
            isLoading: false,
            currentUserQuestion: null,
          ),
        );

        _logger.info('AI问答完成: $question');
      } else {
        _updateState(
          _state.copyWith(
            isLoading: false,
            error: 'AI暂时无法回答，请稍后重试',
            currentUserQuestion: null,
          ),
        );
      }
    } catch (e) {
      _logger.error('AI问答失败', error: e);
      _updateState(
        _state.copyWith(
          isLoading: false,
          error: '提问失败，请检查网络连接',
          currentUserQuestion: null,
        ),
      );
    }
  }

  /// 点击关联问题
  /// 处理用户点击关联问题卡片的交互
  Future<void> onRelatedQuestionTap(
    RelatedQuestion question,
    BuildContext? context,
  ) async {
    try {
      _updateState(_state.setLoading(true));

      // 记录用户行为
      _apiService.recordLearningBehavior(
        action: 'tap_related_question',
        questionId: question.id,
        additionalData: {
          'question_type': question.type.toString(),
          'question_text': question.question,
        },
        context: context,
      );

      KnowledgeCard? newKnowledge;

      if (question.type == QuestionType.next ||
          question.type == QuestionType.deeper) {
        // 对于"下一步"和"深入了解"类型，直接向AI提问
        newKnowledge = await _apiService.askAiQuestion(
          question: question.question,
          context: _state.currentKnowledge?.content,
          buildContext: context,
        );
      } else {
        // 对于其他类型，尝试获取知识详情
        newKnowledge = await _apiService.getKnowledgeDetail(
          questionId: question.id,
          context: context,
        );

        // 如果没有详情，则向AI提问
        if (newKnowledge == null) {
          newKnowledge = await _apiService.askAiQuestion(
            question: question.question,
            context: _state.currentKnowledge?.content,
            buildContext: context,
          );
        }
      }

      if (newKnowledge != null) {
        // 生成新的关联问题
        final newRelatedQuestions = await _apiService
            .generateAiRelatedQuestions(
              currentContent: newKnowledge.content,
              questionTitle: newKnowledge.questionTitle,
              buildContext: context,
            );

        _updateState(
          _state.copyWith(
            currentKnowledge: newKnowledge,
            relatedQuestions: newRelatedQuestions,
            isLoading: false,
          ),
        );

        _logger.info('关联问题处理完成: ${question.question}');
      } else {
        _updateState(_state.copyWith(isLoading: false, error: '无法获取相关内容，请重试'));
      }
    } catch (e) {
      _logger.error('关联问题处理失败', error: e);
      _updateState(_state.copyWith(isLoading: false, error: '加载失败，请检查网络连接'));
    }
  }

  /// 刷新关联问题
  /// 重新生成AI关联问题
  Future<void> refreshRelatedQuestions(BuildContext? context) async {
    if (_state.currentKnowledge == null) return;

    try {
      final newRelatedQuestions = await _apiService.generateAiRelatedQuestions(
        currentContent: _state.currentKnowledge!.content,
        questionTitle: _state.currentKnowledge!.questionTitle,
        buildContext: context,
      );

      _updateState(_state.copyWith(relatedQuestions: newRelatedQuestions));

      _logger.info('关联问题刷新完成');
    } catch (e) {
      _logger.warning('关联问题刷新失败: $e');
      // 不显示错误，静默失败
    }
  }

  /// 清除错误状态
  void clearError() {
    if (_state.error != null) {
      _updateState(_state.clearError());
    }
  }

  /// 重置页面状态
  void reset() {
    _updateState(ExplorePageState());
    _logger.info('探索页面状态已重置');
  }

  /// 更新状态并通知监听者
  void _updateState(ExplorePageState newState) {
    _state = newState;
    notifyListeners();
  }

  @override
  void dispose() {
    _apiService.dispose();
    super.dispose();
  }
}

/// 探索页面状态扩展方法
extension ExplorePageStateExtensions on ExplorePageState {
  /// 是否有内容可显示
  bool get hasContent => currentKnowledge != null;

  /// 是否有关联问题
  bool get hasRelatedQuestions => relatedQuestions.isNotEmpty;

  /// 是否处于初始状态
  bool get isInitial => currentKnowledge == null && !isLoading && error == null;
}
