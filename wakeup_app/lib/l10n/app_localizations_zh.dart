// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Chinese (`zh`).
class AppLocalizationsZh extends AppLocalizations {
  AppLocalizationsZh([String locale = 'zh']) : super(locale);

  @override
  String get appName => '唤醒';

  @override
  String get login => '登录';

  @override
  String get register => '注册';

  @override
  String get phoneNumber => '手机号';

  @override
  String get verificationCode => '验证码';

  @override
  String get sendCode => '发送验证码';

  @override
  String get resend => '重新发送';

  @override
  String get notification => '通知';

  @override
  String get confirm => '确定';

  @override
  String get cancel => '取消';

  @override
  String get pleaseEnterPhoneNumber => '请输入手机号';

  @override
  String get pleaseEnterVerificationCode => '请输入6位验证码';

  @override
  String get codeSent => '验证码已发送';

  @override
  String get loginSuccess => '登录成功';

  @override
  String get loginFailed => '登录失败，请稍后重试';

  @override
  String get loginRequired => '需要登录';

  @override
  String get pleaseLoginFirst => '请先登录以查看课程详情';

  @override
  String get course => '课程';

  @override
  String get courseDetails => '课程详情';

  @override
  String get joinCourse => '加入课程';

  @override
  String get joinCourseSuccess => '成功加入课程';

  @override
  String get joinCourseFailed => '加入课程失败';

  @override
  String error(String error) {
    return '错误: $error';
  }

  @override
  String get settings => '设置';

  @override
  String get language => '语言';

  @override
  String get english => '英文';

  @override
  String get chinese => '中文';

  @override
  String get logout => '退出登录';

  @override
  String get logoutConfirmation => '确定要退出登录吗？';

  @override
  String get loadingFailedSimple => '加载失败';

  @override
  String loadingFailed(String error) {
    return '加载失败: $error';
  }

  @override
  String get unexpectedDataStructure => 'API返回了意外的数据结构';

  @override
  String loadingCourseFailed(String error) {
    return '加载课程失败: $error';
  }

  @override
  String get coursePageTitle => '选题';

  @override
  String get whichCourseInterest => '您对哪些题库感兴趣？';

  @override
  String get courseSelectionHint =>
      '请在下面选择一个，我们将根据您的选择为您匹配答题题库、最新资讯政策以及备考资料来帮助您学习。（您可以随时更新此内容。）';

  @override
  String get clickCardForDetails => '点击卡片进入详情页';

  @override
  String loadingCourseError(String error) {
    return '加载失败: $error';
  }

  @override
  String get uncategorizedCourses => '未分类课程';

  @override
  String get learningPageTitle => '答题';

  @override
  String get pleaseLoginToLearn => '请登录后开始学习';

  @override
  String get loginNow => '立即登录';

  @override
  String get retry => '重试';

  @override
  String get noCourses => '尚未加入任何题库';

  @override
  String get goToCoursePage => '去添加题库';

  @override
  String get startLearning => '滑动开始刷题';

  @override
  String get selectCourseInterest => '请前往课程页面选择您感兴趣的题库';

  @override
  String get myCourses => '我的课程';

  @override
  String get informationHubPageTitle => '资讯库';

  @override
  String get resourceLibraryPageTitle => '资料库';

  @override
  String get accountSettings => '账户设置';

  @override
  String get notificationSettings => '通知设置';

  @override
  String get pushNotifications => '推送通知';

  @override
  String get receiveNotifications => '接收学习提醒和重要更新';

  @override
  String get languageSettings => '语言设置';

  @override
  String get privacySettings => '隐私设置';

  @override
  String get findFriends => '查找好友';

  @override
  String get findFriendsDescription => '允许其他用户通过手机号或用户名找到你';

  @override
  String get editProfile => '编辑个人资料';

  @override
  String get notLoggedIn => '未登录';

  @override
  String get setupProfile => '设置个人资料';

  @override
  String get navLearning => '答题';

  @override
  String get navCourses => '选题';

  @override
  String get navExplore => '探索';

  @override
  String get navResources => '资料库';
}
