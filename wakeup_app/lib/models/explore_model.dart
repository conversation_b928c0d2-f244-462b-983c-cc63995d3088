/// 知识卡片数据模型
/// 用于展示主要的学习内容和AI回答
class KnowledgeCard {
  final String id;
  final String questionTitle; // 问题标题
  final String content; // 正文内容
  final DateTime createdAt;
  final String? sourceType; // 来源类型：'database', 'ai_generated'
  final Map<String, dynamic>? metadata; // 额外元数据

  KnowledgeCard({
    required this.id,
    required this.questionTitle,
    required this.content,
    required this.createdAt,
    this.sourceType,
    this.metadata,
  });

  /// 从JSON创建KnowledgeCard实例
  factory KnowledgeCard.fromJson(Map<String, dynamic> json) {
    return KnowledgeCard(
      id: json['id']?.toString() ?? '',
      questionTitle: json['question_title'] ?? json['questionTitle'] ?? '',
      content: json['content'] ?? '',
      createdAt:
          json['created_at'] != null
              ? DateTime.parse(json['created_at'])
              : DateTime.now(),
      sourceType: json['source_type'] ?? json['sourceType'],
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// 转换为JSON格式
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'question_title': questionTitle,
      'content': content,
      'created_at': createdAt.toIso8601String(),
      'source_type': sourceType,
      'metadata': metadata,
    };
  }

  /// 创建副本并修改部分属性
  KnowledgeCard copyWith({
    String? id,
    String? questionTitle,
    String? content,
    DateTime? createdAt,
    String? sourceType,
    Map<String, dynamic>? metadata,
  }) {
    return KnowledgeCard(
      id: id ?? this.id,
      questionTitle: questionTitle ?? this.questionTitle,
      content: content ?? this.content,
      createdAt: createdAt ?? this.createdAt,
      sourceType: sourceType ?? this.sourceType,
      metadata: metadata ?? this.metadata,
    );
  }
}

/// 关联问题数据模型
/// 用于展示相关的学习问题和下一步学习建议
class RelatedQuestion {
  final String id;
  final String question; // 问题文本
  final String? description; // 问题描述
  final QuestionType type; // 问题类型
  final int priority; // 优先级（用于排序）
  final bool isAiGenerated; // 是否为AI生成

  RelatedQuestion({
    required this.id,
    required this.question,
    this.description,
    required this.type,
    this.priority = 0,
    this.isAiGenerated = false,
  });

  /// 从JSON创建RelatedQuestion实例
  factory RelatedQuestion.fromJson(Map<String, dynamic> json) {
    return RelatedQuestion(
      id: json['id']?.toString() ?? '',
      question: json['question'] ?? '',
      description: json['description'],
      type: QuestionType.fromString(json['type'] ?? 'related'),
      priority: json['priority'] ?? 0,
      isAiGenerated: json['is_ai_generated'] ?? false,
    );
  }

  /// 转换为JSON格式
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'question': question,
      'description': description,
      'type': type.toString(),
      'priority': priority,
      'is_ai_generated': isAiGenerated,
    };
  }
}

/// 问题类型枚举
enum QuestionType {
  related, // 相关问题
  next, // 下一个知识点
  deeper, // 深入探索
  practice; // 实践练习

  /// 从字符串创建QuestionType
  static QuestionType fromString(String value) {
    switch (value.toLowerCase()) {
      case 'related':
        return QuestionType.related;
      case 'next':
        return QuestionType.next;
      case 'deeper':
        return QuestionType.deeper;
      case 'practice':
        return QuestionType.practice;
      default:
        return QuestionType.related;
    }
  }

  /// 获取类型的中文显示名称
  String get displayName {
    switch (this) {
      case QuestionType.related:
        return '相关问题';
      case QuestionType.next:
        return '下一步';
      case QuestionType.deeper:
        return '深入了解';
      case QuestionType.practice:
        return '实践练习';
    }
  }
}

/// 探索页面状态数据模型
/// 管理整个探索页面的状态和数据
class ExplorePageState {
  final KnowledgeCard? currentKnowledge;
  final List<RelatedQuestion> relatedQuestions;
  final bool isLoading;
  final String? error;
  final String? currentUserQuestion; // 用户当前输入的问题

  ExplorePageState({
    this.currentKnowledge,
    this.relatedQuestions = const [],
    this.isLoading = false,
    this.error,
    this.currentUserQuestion,
  });

  /// 创建副本并修改部分属性
  ExplorePageState copyWith({
    KnowledgeCard? currentKnowledge,
    List<RelatedQuestion>? relatedQuestions,
    bool? isLoading,
    String? error,
    String? currentUserQuestion,
  }) {
    return ExplorePageState(
      currentKnowledge: currentKnowledge ?? this.currentKnowledge,
      relatedQuestions: relatedQuestions ?? this.relatedQuestions,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      currentUserQuestion: currentUserQuestion ?? this.currentUserQuestion,
    );
  }

  /// 清除错误状态
  ExplorePageState clearError() {
    return copyWith(error: null);
  }

  /// 设置加载状态
  ExplorePageState setLoading(bool loading) {
    return copyWith(isLoading: loading, error: null);
  }
}

/// AI问答请求数据模型
class AiQuestionRequest {
  final String question;
  final String? context; // 当前知识上下文
  final String? userId; // 用户ID
  final Map<String, dynamic>? additionalData;

  AiQuestionRequest({
    required this.question,
    this.context,
    this.userId,
    this.additionalData,
  });

  /// 转换为JSON格式用于API请求
  Map<String, dynamic> toJson() {
    return {
      'question': question,
      'context': context,
      'user_id': userId,
      'additional_data': additionalData,
    };
  }
}

/// AI问答响应数据模型
class AiQuestionResponse {
  final String answer;
  final List<RelatedQuestion> suggestedQuestions;
  final String? confidence; // AI回答的置信度
  final Map<String, dynamic>? metadata;

  AiQuestionResponse({
    required this.answer,
    this.suggestedQuestions = const [],
    this.confidence,
    this.metadata,
  });

  /// 从JSON创建AiQuestionResponse实例
  factory AiQuestionResponse.fromJson(Map<String, dynamic> json) {
    return AiQuestionResponse(
      answer: json['answer'] ?? '',
      suggestedQuestions:
          (json['suggested_questions'] as List<dynamic>?)
              ?.map((q) => RelatedQuestion.fromJson(q as Map<String, dynamic>))
              .toList() ??
          [],
      confidence: json['confidence'],
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }
}
