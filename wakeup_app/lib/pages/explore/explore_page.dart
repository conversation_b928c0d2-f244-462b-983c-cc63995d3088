import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:mesh/mesh.dart';
import '../../providers/explore_provider.dart';
import '../../widgets/minimal_knowledge_card.dart';
import '../../widgets/minimal_related_questions.dart';
import '../../widgets/minimal_ai_input.dart';
import '../../constants/fonts.dart';

/// 探索页面 - 轻量简约的深度学习交互界面
/// Apple暗夜深邃风格，使用网格渐变
class ExplorePage extends StatefulWidget {
  const ExplorePage({super.key});

  @override
  State<ExplorePage> createState() => _ExplorePageState();
}

class _ExplorePageState extends State<ExplorePage> {
  /// 获取深邃的背景网格渐变
  OMeshRect get _backgroundMesh {
    return OMeshRect(
      width: 3,
      height: 3,
      colorSpace: OMeshColorSpace.lab,
      fallbackColor: const Color(0xFF000000),
      backgroundColor: const Color(0xFF000000),
      vertices: [
        // 简单的3x3网格
        (-0.2, -0.2).v,
        (0.5, -0.1).v,
        (1.2, -0.2).v,

        (-0.1, 0.5).v,
        (0.5, 0.5).v,
        (1.1, 0.5).v,

        (-0.2, 1.2).v,
        (0.5, 1.1).v,
        (1.2, 1.2).v,
      ],
      colors: [
        const Color(0xFF000000),
        const Color(0xFF0A0A0A),
        const Color(0xFF000000),

        const Color(0xFF0A0A0A),
        const Color(0xFF151515),
        const Color(0xFF0A0A0A),

        const Color(0xFF000000),
        const Color(0xFF0A0A0A),
        const Color(0xFF000000),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => ExploreProvider(),
      child: Consumer<ExploreProvider>(
        builder: (context, provider, child) {
          // 初始化Provider（仅在首次构建时）
          if (provider.isInitial && !provider.isLoading) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              provider.initialize(context);
            });
          }

          return CupertinoPageScaffold(
            navigationBar: _buildNavigationBar(provider),
            backgroundColor: Colors.black,
            child: Stack(
              children: [
                // 深邃的网格渐变背景
                OMeshGradient(
                  size: Size.infinite,
                  tessellation: 15,
                  mesh: _backgroundMesh,
                ),

                // 主要内容
                _buildBody(provider),
              ],
            ),
          );
        },
      ),
    );
  }

  /// 构建导航栏
  CupertinoNavigationBar _buildNavigationBar(ExploreProvider provider) {
    return CupertinoNavigationBar(
      backgroundColor: Colors.transparent,
      border: null,
      middle: Text(
        '深度探索',
        style: AppFonts.createMixedStyle(
          fontSize: 17,
          fontWeight: FontWeight.w600,
          color: Colors.white,
          isTitle: true,
        ),
      ),
      trailing:
          provider.hasRelatedQuestions
              ? GestureDetector(
                onTap: () => provider.refreshRelatedQuestions(context),
                child: Icon(
                  CupertinoIcons.refresh,
                  color: Colors.white.withValues(alpha: 0.8),
                  size: 20,
                ),
              )
              : null,
    );
  }

  /// 构建页面主体
  Widget _buildBody(ExploreProvider provider) {
    // 显示错误状态
    if (provider.error != null) {
      return _buildErrorState(provider);
    }

    // 显示初始状态
    if (provider.isInitial) {
      return _buildInitialState();
    }

    return Column(
      children: [
        // 主内容区域（知识卡片 + 关联问题）
        Expanded(child: _buildMainContent(provider)),

        // 底部AI提问输入框
        MinimalAiInput(
          onQuestionSubmitted:
              (question) => provider.askAiQuestion(question, context),
          isLoading: provider.isLoading,
          placeholder: '问我任何问题，开始深度探索...',
        ),
      ],
    );
  }

  /// 构建主内容区域
  Widget _buildMainContent(ExploreProvider provider) {
    return SingleChildScrollView(
      physics: const BouncingScrollPhysics(),
      child: Column(
        children: [
          const SizedBox(height: 20),

          // 主知识卡片
          if (provider.currentKnowledge != null)
            MinimalKnowledgeCard(
              knowledge: provider.currentKnowledge!,
              isLoading: provider.isLoading,
              onTap: () {
                // 可以添加卡片点击事件，比如全屏显示
              },
            )
          else if (provider.isLoading)
            _buildLoadingKnowledgeCard()
          else
            _buildEmptyKnowledgeCard(),

          const SizedBox(height: 24),

          // 关联问题导航
          if (provider.relatedQuestions.isNotEmpty)
            MinimalRelatedQuestions(
              questions: provider.relatedQuestions,
              onQuestionTap:
                  (question) =>
                      provider.onRelatedQuestionTap(question, context),
              isLoading: provider.isLoading,
            )
          else if (provider.isLoading)
            MinimalRelatedQuestionsSkeleton()
          else
            _buildEmptyRelatedQuestions(),

          const SizedBox(height: 100), // 为底部输入框留出空间
        ],
      ),
    );
  }

  /// 构建错误状态
  Widget _buildErrorState(ExploreProvider provider) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              CupertinoIcons.exclamationmark_triangle,
              size: 64,
              color: Colors.red.withValues(alpha: 0.7),
            ),
            const SizedBox(height: 16),
            Text(
              '出现了一些问题',
              style: AppFonts.createMixedStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.white,
                isTitle: true,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              provider.error!,
              style: AppFonts.createMixedStyle(
                fontSize: 16,
                color: Colors.white.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            CupertinoButton.filled(
              onPressed: () {
                provider.clearError();
                provider.initialize(context);
              },
              child: const Text('重试'),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建初始状态
  Widget _buildInitialState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              CupertinoIcons.compass,
              size: 80,
              color: Colors.blue.withValues(alpha: 0.7),
            ),
            const SizedBox(height: 24),
            Text(
              '开始你的深度探索之旅',
              style: AppFonts.createMixedStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.white,
                isTitle: true,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Text(
              '在下方输入任何问题\n让AI为你提供详细解答',
              style: AppFonts.createMixedStyle(
                fontSize: 16,
                color: Colors.white.withValues(alpha: 0.7),
                height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            _buildQuickStartSuggestions(),
          ],
        ),
      ),
    );
  }

  /// 构建加载状态的知识卡片
  Widget _buildLoadingKnowledgeCard() {
    return Container(
      height: 320,
      margin: const EdgeInsets.symmetric(horizontal: 20),
      decoration: BoxDecoration(
        color: const Color(0xFF0A0A0A),
        borderRadius: BorderRadius.circular(32),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.05),
          width: 0.5,
        ),
      ),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CupertinoActivityIndicator(radius: 16, color: Colors.white),
            SizedBox(height: 16),
            Text(
              'AI正在思考中...',
              style: TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w400,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建空知识卡片占位符
  Widget _buildEmptyKnowledgeCard() {
    return Container(
      height: 320,
      margin: const EdgeInsets.symmetric(horizontal: 20),
      decoration: BoxDecoration(
        color: const Color(0xFF0A0A0A).withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(32),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.05),
          width: 0.5,
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              CupertinoIcons.doc_text,
              size: 48,
              color: Colors.white.withValues(alpha: 0.2),
            ),
            const SizedBox(height: 16),
            Text(
              '暂无内容',
              style: AppFonts.createMixedStyle(
                fontSize: 16,
                color: Colors.white.withValues(alpha: 0.4),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建空关联问题占位符
  Widget _buildEmptyRelatedQuestions() {
    return Container(
      height: 120,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: List.generate(
          3,
          (index) => Expanded(
            child: Container(
              margin: EdgeInsets.only(right: index < 2 ? 12 : 0),
              decoration: BoxDecoration(
                color: const Color(0xFF0A0A0A).withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.1),
                  width: 1,
                ),
              ),
              child: Center(
                child: Icon(
                  CupertinoIcons.question_circle,
                  size: 24,
                  color: Colors.white.withValues(alpha: 0.3),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建快速开始建议
  Widget _buildQuickStartSuggestions() {
    final suggestions = ['什么是人工智能？', '如何学习编程？', '深度学习的应用', '机器学习入门'];

    return Column(
      children: [
        Text(
          '试试这些问题：',
          style: AppFonts.createMixedStyle(
            fontSize: 14,
            color: Colors.white.withValues(alpha: 0.6),
          ),
        ),
        const SizedBox(height: 16),
        Wrap(
          spacing: 12,
          runSpacing: 12,
          children:
              suggestions
                  .map((suggestion) => _buildSuggestionChip(suggestion))
                  .toList(),
        ),
      ],
    );
  }

  /// 构建建议芯片
  Widget _buildSuggestionChip(String text) {
    return GestureDetector(
      onTap: () {
        final provider = Provider.of<ExploreProvider>(context, listen: false);
        provider.askAiQuestion(text, context);
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        decoration: BoxDecoration(
          color: Colors.blue.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: Colors.blue.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Text(
          text,
          style: AppFonts.createMixedStyle(
            fontSize: 14,
            color: Colors.blue.withValues(alpha: 0.9),
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }
}
