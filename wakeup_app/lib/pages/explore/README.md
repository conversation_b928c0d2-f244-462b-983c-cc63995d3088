# 探索页面 - 深度学习交互界面

## 功能概述

探索页面是一个支持"刨根问底"式知识探索的深度学习交互界面，帮助用户进行深入的学习和思考。

## 页面结构

### 1. 主知识卡片区域
- **位置**: 页面顶部，占据最大显示空间
- **功能**: 展示当前学习的知识内容
- **组件**: `KnowledgeCardWidget`
- **内容结构**:
  - 问题标题区域
  - 正文内容区域
  - 来源标识（AI生成/知识库）
  - 时间信息

### 2. 关联问题导航区域
- **位置**: 主卡片下方
- **功能**: 提供三个相关学习方向
- **组件**: `RelatedQuestionsNavigation`
- **布局**: 三个横向排列的卡片
  - 左侧：相关问题
  - 中间：下一步学习
  - 右侧：深入探索

### 3. AI提问交互区域
- **位置**: 页面底部
- **功能**: 用户输入问题，AI提供回答
- **组件**: `AiQuestionInput`
- **特性**:
  - 毛玻璃效果输入框
  - 快捷问题建议
  - 语音输入支持（可选）

## 技术架构

### 数据模型
- `KnowledgeCard`: 知识卡片数据模型
- `RelatedQuestion`: 关联问题数据模型
- `ExplorePageState`: 页面状态管理
- `AiQuestionRequest/Response`: AI问答数据模型

### 状态管理
- `ExploreProvider`: 使用Provider模式管理页面状态
- 支持加载状态、错误处理、数据更新

### API服务
- `ExploreApiService`: 专门的API服务类
- 支持知识获取、AI问答、关联问题生成
- 集成统一API服务架构

### UI组件
- `KnowledgeCardWidget`: 主知识卡片组件
- `RelatedQuestionsNavigation`: 关联问题导航组件
- `AiQuestionInput`: AI提问输入组件

## 交互流程

### 1. 页面初始化
```
用户进入页面 → 加载初始知识内容 → 生成关联问题 → 显示界面
```

### 2. AI提问流程
```
用户输入问题 → 发送到AI服务 → 获取回答 → 更新知识卡片 → 生成新的关联问题
```

### 3. 关联问题点击
```
用户点击关联问题 → 获取详细内容 → 更新主卡片 → 生成新的关联问题
```

## 设计特色

### 视觉设计
- **黑色主题**: 符合应用整体风格
- **毛玻璃效果**: 现代化的视觉体验
- **渐变边框**: 增强视觉层次
- **响应式布局**: 适配不同屏幕尺寸

### 交互设计
- **流畅动画**: 提升用户体验
- **加载状态**: 清晰的反馈机制
- **错误处理**: 友好的错误提示
- **快捷操作**: 便于快速探索

### 技术特色
- **组件化**: 高度模块化的组件设计
- **状态管理**: 清晰的状态管理架构
- **API集成**: 统一的API服务架构
- **性能优化**: 合理的加载和缓存策略

## 文件结构

```
lib/pages/explore/
├── explore_page.dart          # 主页面文件
├── README.md                  # 说明文档

lib/widgets/
├── knowledge_card.dart        # 知识卡片组件
├── related_questions_navigation.dart  # 关联问题导航
├── ai_question_input.dart     # AI提问输入组件

lib/models/
├── explore_model.dart         # 数据模型定义

lib/services/
├── explore_api_service.dart   # API服务类

lib/providers/
├── explore_provider.dart      # 状态管理Provider
```

## 使用方式

### 1. 在路由中注册
```dart
// 在应用路由中添加探索页面
'/explore': (context) => const ExplorePage(),
```

### 2. 导航到页面
```dart
Navigator.pushNamed(context, '/explore');
```

### 3. 集成到主导航
```dart
// 在底部导航栏中添加探索选项
BottomNavigationBarItem(
  icon: Icon(CupertinoIcons.compass),
  label: '探索',
),
```

## 后端API要求

### 必需的API端点
- `GET /api/explore/initial` - 获取初始知识内容
- `POST /api/explore/ai-question` - AI问答
- `GET /api/explore/related-questions` - 获取关联问题
- `POST /api/explore/generate-related` - 生成AI关联问题
- `GET /api/explore/knowledge/{id}` - 获取知识详情
- `POST /api/explore/behavior` - 记录学习行为

### API响应格式
所有API应返回统一的响应格式，包含成功状态、数据和错误信息。

## 扩展功能

### 可能的扩展方向
1. **语音交互**: 集成语音识别和语音合成
2. **个性化推荐**: 基于用户行为的智能推荐
3. **学习路径**: 生成个性化的学习路径
4. **社交功能**: 分享知识和讨论
5. **离线模式**: 支持离线浏览和学习

### 性能优化
1. **懒加载**: 按需加载内容
2. **缓存策略**: 智能缓存常用内容
3. **预加载**: 预测性内容加载
4. **图片优化**: 优化图片加载和显示

## 注意事项

1. **网络处理**: 确保良好的网络错误处理
2. **状态管理**: 避免内存泄漏和状态不一致
3. **用户体验**: 保持流畅的交互体验
4. **数据安全**: 保护用户隐私和数据安全
5. **测试覆盖**: 确保充分的测试覆盖率

## 开发建议

1. **渐进式开发**: 先实现核心功能，再添加高级特性
2. **用户反馈**: 收集用户反馈，持续优化体验
3. **性能监控**: 监控页面性能，及时优化
4. **A/B测试**: 通过A/B测试优化交互设计
5. **文档维护**: 保持文档的及时更新
